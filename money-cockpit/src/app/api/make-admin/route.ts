import { NextResponse } from 'next/server'
import { createRouteClient } from '@/lib/supabase-server'
import { createClient } from '@supabase/supabase-js'

export async function GET() {
  try {
    // Create a route handler client
    const supabase = createRouteClient()
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }
    
    // Create admin client
    const supabaseAdmin = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!,
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )
    
    // Add user as admin to default workspace
    const { data, error } = await supabaseAdmin
      .from('workspace_users')
      .insert({
        workspace_id: 'cd8d82fd-22c3-4cdf-843f-fce416770014',
        user_id: user.id,
        role: 'admin'
      })
      .select()
      .single()
    
    if (error) {
      // Check if already exists
      if (error.code === '23505') {
        // Update to admin
        const { data: updateData, error: updateError } = await supabaseAdmin
          .from('workspace_users')
          .update({ role: 'admin' })
          .eq('workspace_id', 'cd8d82fd-22c3-4cdf-843f-fce416770014')
          .eq('user_id', user.id)
          .select()
          .single()
        
        if (updateError) {
          return NextResponse.json({ error: updateError.message }, { status: 400 })
        }
        
        return NextResponse.json({ 
          message: 'Updated to admin successfully',
          data: updateData
        })
      }
      
      return NextResponse.json({ error: error.message }, { status: 400 })
    }
    
    // Also ensure profile exists
    await supabaseAdmin
      .from('profiles')
      .upsert({
        id: user.id,
        email: user.email || ''
      }, {
        onConflict: 'id'
      })
    
    return NextResponse.json({ 
      message: 'Added as admin successfully',
      data
    })
    
  } catch (error) {
    console.error('Error in make-admin API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 