import { NextRequest, NextResponse } from 'next/server'
import { createRouteClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest) {
  try {
    const { email, role, workspace_id } = await request.json()

    if (!email || !role || !workspace_id) {
      return NextResponse.json(
        { error: 'Email, role, and workspace_id are required' },
        { status: 400 }
      )
    }

    if (!['admin', 'viewer'].includes(role)) {
      return NextResponse.json(
        { error: 'Role must be admin or viewer' },
        { status: 400 }
      )
    }

    // Create a route handler client
    const supabase = createRouteClient()
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    // Check if user is admin of the workspace
    const { data: isAdmin } = await supabase
      .from('workspace_users')
      .select('role')
      .eq('workspace_id', workspace_id)
      .eq('user_id', user.id)
      .eq('role', 'admin')
      .single()

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'You must be an admin to invite users' },
        { status: 403 }
      )
    }

    // Create invitation
    const { data: invitation, error: inviteError } = await supabase
      .from('workspace_invitations')
      .insert({
        workspace_id,
        email,
        role,
        invited_by: user.id
      })
      .select()
      .single()

    if (inviteError) {
      console.error('Error creating invitation:', inviteError)
      if (inviteError.code === '23505') {
        return NextResponse.json(
          { error: 'User already invited to this workspace' },
          { status: 400 }
        )
      }
      return NextResponse.json(
        { error: inviteError.message },
        { status: 400 }
      )
    }

    // TODO: Send invitation email with the token
    // For now, just return the invitation data
    
    return NextResponse.json({ 
      success: true,
      invitation: {
        id: invitation.id,
        email: invitation.email,
        role: invitation.role,
        expires_at: invitation.expires_at,
        token: invitation.token // In production, this would be sent via email
      }
    })

  } catch (error) {
    console.error('Error in invite API:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 