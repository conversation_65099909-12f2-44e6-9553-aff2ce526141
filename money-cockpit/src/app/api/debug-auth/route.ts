import { NextRequest, NextResponse } from 'next/server'
import { createRouteClient } from '@/lib/supabase-server'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    // Log all cookies
    const cookieStore = await cookies()
    const allCookies = cookieStore.getAll()
    
    console.log('All cookies:', allCookies.map((c: { name: string; value: string }) => ({ name: c.name, value: c.value.substring(0, 20) + '...' })))
    
    // Create a route handler client
    const supabase = createRouteClient()
    
    // Get the session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()
    
    console.log('Session:', session ? 'exists' : 'null')
    console.log('Session error:', sessionError)
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    console.log('User:', user ? user.id : 'null')
    console.log('User error:', userError)
    
    return NextResponse.json({
      cookies: allCookies.map((c: { name: string; value: string }) => ({ name: c.name, hasValue: !!c.value })),
      session: session ? {
        access_token: session.access_token.substring(0, 20) + '...',
        user: session.user.id
      } : null,
      user: user ? {
        id: user.id,
        email: user.email
      } : null,
      errors: {
        session: sessionError?.message,
        user: userError?.message
      }
    })
  } catch (error) {
    console.error('Debug auth error:', error)
    return NextResponse.json({ error: 'Internal server error', details: error }, { status: 500 })
  }
} 