import { NextRequest, NextResponse } from 'next/server'
import { createRouteClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest) {
  try {
    const { name, is_private = true } = await request.json()
    
    if (!name) {
      return NextResponse.json({ error: 'Workspace name is required' }, { status: 400 })
    }
    
    // Create a route handler client
    const supabase = createRouteClient()
    
    // Get the current user
    const { data: { user }, error: userError } = await supabase.auth.getUser()
    
    if (userError || !user) {
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }
    
    // Call the database function to create workspace
    const { data: workspace, error: workspaceError } = await supabase
      .rpc('create_workspace', {
        p_name: name,
        p_user_id: user.id,
        p_is_private: is_private
      })
    
    if (workspaceError) {
      console.error('Error creating workspace:', workspaceError)
      return NextResponse.json({ error: workspaceError.message }, { status: 400 })
    }
    
    return NextResponse.json({ 
      message: 'Workspace created successfully',
      workspace
    })
    
  } catch (error) {
    console.error('Error creating workspace:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
} 