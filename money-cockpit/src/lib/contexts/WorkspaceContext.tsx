'use client'

import { create<PERSON>ontext, use<PERSON>ontext, useEffect, useState, ReactNode } from 'react'
import { createBrowserClient } from '@/lib/supabase-client'

interface Workspace {
  workspace_id: string
  workspace_name: string
  user_role: 'admin' | 'viewer'
  is_private?: boolean
}

interface WorkspaceContextType {
  workspaces: Workspace[]
  currentWorkspace: Workspace | null
  currentWorkspaceId: string | null
  isLoading: boolean
  setCurrentWorkspaceId: (id: string) => void
  refreshWorkspaces: () => Promise<void>
  isAdmin: boolean
}

const WorkspaceContext = createContext<WorkspaceContextType | undefined>(undefined)

export function WorkspaceProvider({ children }: { children: ReactNode }) {
  const [workspaces, setWorkspaces] = useState<Workspace[]>([])
  const [currentWorkspaceId, setCurrentWorkspaceIdState] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const supabase = createBrowserClient()

  const currentWorkspace = workspaces.find(w => w.workspace_id === currentWorkspaceId) || null
  const isAdmin = currentWorkspace?.user_role === 'admin'

  const fetchWorkspaces = async () => {
    try {
      const { data, error } = await supabase.rpc('get_user_workspaces')
      
      if (error) {
        console.error('Error fetching workspaces:', error)
        return
      }

      setWorkspaces(data || [])
      
      // Set current workspace if not set
      if (!currentWorkspaceId && data && data.length > 0) {
        const savedWorkspaceId = localStorage.getItem('currentWorkspaceId')
        const workspaceToSet = savedWorkspaceId && data.find((w: Workspace) => w.workspace_id === savedWorkspaceId)
          ? savedWorkspaceId
          : data[0].workspace_id
        
        setCurrentWorkspaceIdState(workspaceToSet)
        localStorage.setItem('currentWorkspaceId', workspaceToSet)
      }
    } catch (error) {
      console.error('Error fetching workspaces:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const setCurrentWorkspaceId = (id: string) => {
    setCurrentWorkspaceIdState(id)
    localStorage.setItem('currentWorkspaceId', id)
  }

  const refreshWorkspaces = async () => {
    await fetchWorkspaces()
  }

  useEffect(() => {
    // Load saved workspace ID from localStorage
    const savedWorkspaceId = localStorage.getItem('currentWorkspaceId')
    if (savedWorkspaceId) {
      setCurrentWorkspaceIdState(savedWorkspaceId)
    }
    
    fetchWorkspaces()
  }, [])

  return (
    <WorkspaceContext.Provider
      value={{
        workspaces,
        currentWorkspace,
        currentWorkspaceId,
        isLoading,
        setCurrentWorkspaceId,
        refreshWorkspaces,
        isAdmin
      }}
    >
      {children}
    </WorkspaceContext.Provider>
  )
}

export function useWorkspace() {
  const context = useContext(WorkspaceContext)
  if (context === undefined) {
    throw new Error('useWorkspace must be used within a WorkspaceProvider')
  }
  return context
} 