import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'

// For React Server Components
export const createServerClient = () => {
  return createServerComponentClient({ cookies })
}

// For API Route Handlers
export const createRouteClient = () => {
  return createRouteHandlerClient({ cookies })
} 